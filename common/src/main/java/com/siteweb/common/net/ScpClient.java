package com.siteweb.common.net;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.schmizz.sshj.SSHClient;
import net.schmizz.sshj.sftp.RemoteResourceInfo;
import net.schmizz.sshj.sftp.SFTPClient;
import net.schmizz.sshj.sftp.SFTPException;
import net.schmizz.sshj.transport.verification.PromiscuousVerifier;
import net.schmizz.sshj.xfer.FileSystemFile;

import java.io.File;
import java.io.IOException;
import java.time.Duration;
import java.util.List;
import java.util.Objects;


@Slf4j
@Data
public class ScpClient {
    /** 服务器地址 */
    private String host = "localhost";
    /** SSH 端口，默认 22 */
    private int port = 22;
    /** 用户名 */
    private String username = "root";
    /** 密码 */
    private String password = "";
    /** 远程路径，仅做记录 */
    private String remotePath = ".";
    /** 是否开启 keep-alive */
    private boolean keepAlive = true;

    private SSHClient sshClient;

    public ScpClient(String host, int port) {
        this.host = host;
        this.port = port;
        initClient();
    }

    public ScpClient(String host, int port, String username, String password) {
        this.host = host;
        this.port = port;
        this.username = username;
        this.password = password;
        initClient();
    }

    /**
     * 初始化 SSHClient 并设置基本参数
     */
    private void initClient() {
        this.sshClient = new SSHClient();
        this.sshClient.addHostKeyVerifier(new PromiscuousVerifier());
        // 配置超时
        this.sshClient.setConnectTimeout((int) Duration.ofSeconds(10).toMillis());
        this.sshClient.setTimeout((int) Duration.ofSeconds(30).toMillis());
    }

    /**
     * 连接并认证到 SSH 服务器
     */
    public void login() throws IOException {
        sshClient.connect(host, port);
        sshClient.authPassword(username, password);
        if (keepAlive) {
            sshClient.getConnection().getKeepAlive().setKeepAliveInterval(30);
        }
        log.info("成功连接到 SSH 服务器: {}:{}", host, port);
    }

    /**
     * 断开连接
     */
    public void logout() {
        if (sshClient != null && sshClient.isConnected()) {
            try {
                sshClient.disconnect();
                log.info("已断开与 SSH 服务器的连接。");
            } catch (IOException e) {
                log.error("断开 SSH 服务器时出错: {}", e.getMessage());
            }
        }
    }

    /**
     * 切换远程目录(仅修改内部记录，不执行远程命令)
     */
    public void changeDirectory(String dirName) {
        this.remotePath = dirName;
    }

    /**
     * 递归下载目录
     *
     * @param remoteDirPath   远程目录
     * @param localParentDir  本地目标目录
     * @param excludePatterns 排除规则
     */
    public void downloadDirectory(String remoteDirPath, String localParentDir, List<String> excludePatterns) throws IOException {
        try (SFTPClient sftpClient = sshClient.newSFTPClient()) {
            List<RemoteResourceInfo> files = sftpClient.ls(remoteDirPath);
            if (Objects.isNull(files)) {
                return;
            }

            for (RemoteResourceInfo info : files) {
                String fileName = info.getName();

                if (shouldExclude(fileName, excludePatterns)) {
                    log.info("排除文件/目录: {}", fileName);
                    continue;
                }

                String remoteFilePath = remoteDirPath + "/" + fileName;
                File localFile = new File(localParentDir, fileName);

                if (info.isDirectory()) {
                    if (!localFile.exists() && !localFile.mkdirs()) {
                        throw new IOException("创建本地目录失败: " + localFile.getAbsolutePath());
                    }
                    downloadDirectory(remoteFilePath, localFile.getAbsolutePath(), excludePatterns);
                } else {
                    // 使用 SCP 进行文件下载
                    sshClient.newSCPFileTransfer()
                            .download(remoteFilePath, new FileSystemFile(localFile));
                    log.info("文件下载成功: {}", remoteFilePath);
                }
            }
        } catch (SFTPException e) {
            throw new IOException("下载目录时发生 SFTP 错误: " + e.getMessage(), e);
        }
    }

    private boolean shouldExclude(String fileName, List<String> excludePatterns) {
        if (excludePatterns == null || excludePatterns.isEmpty()) {
            return false;
        }
        for (String pattern : excludePatterns) {
            if (pattern.contains("*")) {
                String regex = pattern.replace(".", "\\.").replace("*", ".*");
                if (fileName.matches(regex)) {
                    return true;
                }
            } else if (fileName.equals(pattern)) {
                return true;
            }
        }
        return false;
    }
}