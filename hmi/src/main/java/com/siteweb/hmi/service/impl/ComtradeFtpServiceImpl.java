package com.siteweb.hmi.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.siteweb.hmi.dto.VendorTransferCfg;
import com.siteweb.common.net.FtpClient;
import com.siteweb.common.net.ScpClient;
import com.siteweb.hmi.service.ComtradeFileCleanupService;
import com.siteweb.hmi.service.ComtradeFtpService;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.Port;
import com.siteweb.monitoring.entity.SamplerUnit;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.PortService;
import com.siteweb.monitoring.service.SamplerUnitService;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import net.schmizz.sshj.sftp.RemoteResourceInfo;
import net.schmizz.sshj.sftp.SFTPClient;
import net.schmizz.sshj.xfer.FileSystemFile;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.apache.commons.net.ftp.FTPFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2025/5/15
 */
@Service
@Slf4j
public class ComtradeFtpServiceImpl implements ComtradeFtpService {

    @Autowired
    private EquipmentService equipmentService;

    @Autowired
    private SamplerUnitService samplerUnitService;

    @Autowired
    private PortService portService;

    @Autowired
    private ComtradeFileCleanupService comtradeFileCleanupService;
    
    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 根据设备ID获取对应的IP地址
     * <p>
     * 该方法通过设备ID查找关联的采样单元和端口信息，从端口的setting字段中提取IP地址。
     * 查找过程：设备 -> 采样单元 -> 端口 -> 端口配置中的IP地址
     *f
     * @param equipmentId 设备ID
     * @return 设备对应的IP地址，如果任何步骤失败则返回空字符串
     */
    @Override
    public String getIpAddressByEquipmentId(Integer equipmentId) {
        // 获取设备对象
        Equipment equipment = equipmentService.findById(equipmentId);
        if (equipment == null) {
            return "";
        }

        // 获取采样单元对象
        Integer samplerUnitId = equipment.getSamplerUnitId();
        if (samplerUnitId == null) {
            return "";
        }

        SamplerUnit samplerUnit = samplerUnitService.findBySamplerUnitId(samplerUnitId);
        if (samplerUnit == null) {
            return "";
        }

        // 获取端口对象
        Integer portId = samplerUnit.getPortId();
        if (portId == null) {
            return "";
        }

        Port port = portService.findByPortIdAndMonitorUnitId(portId, samplerUnit.getMonitorUnitId());
        if (port == null) {
            return "";
        }

        // setting字段中获取IP地址
        String setting = port.getSetting();
        if (setting == null) {
            return "";
        }
        String ip = setting.replaceAll("(.*?)(\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})(.*)", "$2");
        return ip;
    }

    /**
     * 从指定IP地址的FTP服务器下载最新的COMTRADE文件
     * <p>
     * 该方法将只下载最近生成的COMTRADE文件，避免重复下载历史文件。
     * 具体实现待完成。
     *
     * @param ipAddress   FTP服务器的IP地址
     * @param equipmentId 设备ID
     * @return 成功下载的最新文件数量，当前返回0
     */
    @Override
    public int downloadLatestComtradeFiles(String ipAddress, Integer equipmentId) {
        if (ipAddress == null || ipAddress.isEmpty() || equipmentId == null) {
            return 0;
        }

        // 获取设备的厂商名称
        Equipment equipment = equipmentService.findById(equipmentId);
        if (equipment == null) {
            return 0;
        }
        String vendor = equipment.getVendor();


        // 获取厂商传输配置
        VendorTransferCfg vendorConfig = getVendorConfigOrDefault(vendor);
        String protocol = vendorConfig.getProtocol();
        Integer port = vendorConfig.getPort();
        String username = vendorConfig.getUsername();
        String password = vendorConfig.getPassword();
        String directory = vendorConfig.getDirectory();
        
        // 根据协议类型选择不同的下载方式
        if ("SCP".equalsIgnoreCase(protocol)) {
            return downloadLatestComtradeFilesByScp(ipAddress, equipmentId, port, username, password, directory);
        } else {
            return downloadLatestComtradeFilesByFtp(ipAddress, equipmentId, port, username, password, directory);
        }
    }
    
    /**
     * 通过FTP协议下载最新的COMTRADE文件
     */
    private int downloadLatestComtradeFilesByFtp(String ipAddress, Integer equipmentId, Integer port, String username, String password, String directory) {
        // 创建FTP客户端，如果有用户名和密码则使用，否则使用匿名登录
        FTPClientConfig ftpConfig = new FTPClientConfig(FTPClientConfig.SYST_UNIX);
        FtpClient ftpClient;
        if (StrUtil.isNotBlank(username) && StrUtil.isNotBlank(password)) {
            ftpClient = new FtpClient(ipAddress, port, username, password, ftpConfig);
        } else {
            ftpClient = new FtpClient(ipAddress, port);
        }
        
        String localPath = "upload-dir/comtrade/" + equipmentId;
        File localDir = new File(localPath);

        if (!localDir.exists()) {
            localDir.mkdirs();
        }

        try {
            // 获取本地文件列表
            Set<String> localFiles = new HashSet<>();
            if (localDir.exists()) {
                File[] files = localDir.listFiles();
                if (files != null) {
                    for (File file : files) {
                        localFiles.add(file.getName());
                    }
                }
            } else {
                localDir.mkdirs();
            }

            // 连接FTP服务器并获取文件列表
            ftpClient.login();
            ftpClient.changeDirectory(directory);
            FTPFile[] ftpFiles = ftpClient.getApacheFtpClient().listFiles();

            // 筛选需要下载的文件
            List<String> filesToDownload = new ArrayList<>();
            for (FTPFile ftpFile : ftpFiles) {
                // 忽略目录，只处理文件
                if (ftpFile.isDirectory()) {
                    continue;
                }
                
                String fileName = ftpFile.getName();
                String lowerFileName = fileName.toLowerCase();
                // 只处理.cfg和.dat文件（忽略大小写）
                if (!(lowerFileName.endsWith(".cfg") || lowerFileName.endsWith(".dat"))) {
                    continue;
                }
                
                if (!localFiles.contains(fileName)) {
                    filesToDownload.add(fileName);

                    // 确保成对下载.cfg和.dat文件（忽略大小写）
                    if (lowerFileName.endsWith(".cfg")) {
                        String baseName = fileName.substring(0, fileName.length() - 4);
                        // 查找对应的.dat文件（可能是大写或小写）
                        String datFile = findMatchingFile(ftpFiles, baseName, ".dat");
                        if (datFile != null && !localFiles.contains(datFile)) {
                            filesToDownload.add(datFile);
                        }
                    } else if (lowerFileName.endsWith(".dat")) {
                        String baseName = fileName.substring(0, fileName.length() - 4);
                        // 查找对应的.cfg文件（可能是大写或小写）
                        String cfgFile = findMatchingFile(ftpFiles, baseName, ".cfg");
                        if (cfgFile != null && !localFiles.contains(cfgFile)) {
                            filesToDownload.add(cfgFile);
                        }
                    }
                }
            }

            // 下载文件
            int downloadedCount = 0;
            for (String fileName : filesToDownload) {
                try (OutputStream outputStream = new FileOutputStream(new File(localDir, fileName))) {
                    if (ftpClient.getApacheFtpClient().retrieveFile(fileName, outputStream)) {
                        downloadedCount++;
                        log.info("下载最新COMTRADE文件成功: {}", fileName);
                    }
                }
            }

            ftpClient.logout();
            
            // 下载完成后执行文件清理
            if (downloadedCount > 0) {
                try {
                    int cleanedCount = comtradeFileCleanupService.cleanupComtradeFiles(equipmentId);
                    if (cleanedCount > 0) {
                        log.info("设备ID: {} 下载完成后清理了 {} 个旧文件对", equipmentId, cleanedCount);
                    }
                } catch (Exception e) {
                    log.error("设备ID: {} 下载后执行文件清理时出错", equipmentId, e);
                }
            }
            
            return downloadedCount;

        } catch (IOException e) {
            log.error("通过FTP下载最新COMTRADE文件失败: {}", e.getMessage());
            return 0;
        }
    }
    
    /**
     * 通过SCP协议下载最新的COMTRADE文件
     */
    private int downloadLatestComtradeFilesByScp(String ipAddress, Integer equipmentId, Integer port, String username, String password, String directory) {
        try {
            // 创建本地目录
            String localDir = "upload-dir/comtrade/" + equipmentId + "/";
            File localDirFile = new File(localDir);
            if (!localDirFile.exists() && !localDirFile.mkdirs()) {
                log.error("创建本地目录失败: {}", localDir);
                return 0;
            }
            
            // 获取本地已存在的文件列表
            Set<String> localFiles = new HashSet<>();
            File[] existingFiles = localDirFile.listFiles();
            if (existingFiles != null) {
                for (File file : existingFiles) {
                    if (file.isFile()) {
                        localFiles.add(file.getName());
                    }
                }
            }
            
            // 创建SCP客户端
            ScpClient scpClient = new ScpClient(ipAddress, port, username, password);
            scpClient.login();
            
            // 获取远程文件列表
            List<String> remoteFiles = new ArrayList<>();
            try (SFTPClient sftpClient = scpClient.getSshClient().newSFTPClient()) {
                List<RemoteResourceInfo> files = sftpClient.ls(directory);
                if (files != null) {
                    for (RemoteResourceInfo info : files) {
                        if (!info.isDirectory()) {
                            String fileName = info.getName();
                            String lowerFileName = fileName.toLowerCase();
                            // 只处理.cfg和.dat文件（忽略大小写）
                            if (lowerFileName.endsWith(".cfg") || lowerFileName.endsWith(".dat")) {
                                remoteFiles.add(fileName);
                            }
                        }
                    }
                }
            }
            
            // 筛选需要下载的文件
            Set<String> filesToDownload = new HashSet<>();
            for (String fileName : remoteFiles) {
                String lowerFileName = fileName.toLowerCase();
                
                if (!localFiles.contains(fileName)) {
                    filesToDownload.add(fileName);
                    
                    // 确保成对下载.cfg和.dat文件（忽略大小写）
                    if (lowerFileName.endsWith(".cfg")) {
                        String baseName = fileName.substring(0, fileName.length() - 4);
                        // 查找对应的.dat文件（可能是大写或小写）
                        String datFile = findMatchingScpFile(remoteFiles, baseName, ".dat");
                        if (datFile != null && !localFiles.contains(datFile)) {
                            filesToDownload.add(datFile);
                        }
                    } else if (lowerFileName.endsWith(".dat")) {
                        String baseName = fileName.substring(0, fileName.length() - 4);
                        // 查找对应的.cfg文件（可能是大写或小写）
                        String cfgFile = findMatchingScpFile(remoteFiles, baseName, ".cfg");
                        if (cfgFile != null && !localFiles.contains(cfgFile)) {
                            filesToDownload.add(cfgFile);
                        }
                    }
                }
            }
            
            // 下载文件
            int downloadedCount = 0;
            for (String fileName : filesToDownload) {
                try {
                    String remoteFilePath = directory + "/" + fileName;
                    File localFile = new File(localDir, fileName);
                    
                    // 使用SCP进行文件下载
                     scpClient.getSshClient().newSCPFileTransfer()
                             .download(remoteFilePath, new FileSystemFile(localFile));
                    
                    downloadedCount++;
                    log.info("通过SCP下载最新COMTRADE文件成功: {}", fileName);
                } catch (Exception e) {
                    log.error("通过SCP下载文件失败: {}, 错误: {}", fileName, e.getMessage());
                }
            }
            
            scpClient.logout();
            
            // 下载完成后执行文件清理
            if (downloadedCount > 0) {
                try {
                    int cleanedCount = comtradeFileCleanupService.cleanupComtradeFiles(equipmentId);
                    if (cleanedCount > 0) {
                        log.info("设备ID: {} 下载完成后清理了 {} 个旧文件对", equipmentId, cleanedCount);
                    }
                } catch (Exception e) {
                    log.error("设备ID: {} 下载后执行文件清理时出错", equipmentId, e);
                }
            }
            
            return downloadedCount;
            
        } catch (Exception e) {
            log.error("通过SCP下载最新COMTRADE文件失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 获取指定设备的COMTRADE文件列表
     * <p>
     * 该方法将返回指定设备ID对应目录下的所有COMTRADE文件名列表。
     * 具体实现待完成。
     *
     * @param equipmentId 设备ID
     * @return COMTRADE文件名列表，当前返回空列表
     */
    @Override
    public List<String> getComtradeFileList(Integer equipmentId) {
        return List.of();
    }

    /**
     * 查找匹配的文件名（忽略大小写）
     * 
     * @param ftpFiles FTP文件列表
     * @param baseName 基础文件名（不包含扩展名）
     * @param extension 扩展名（如".cfg"或".dat"）
     * @return 匹配的文件名，如果未找到则返回null
     */
    private String findMatchingFile(FTPFile[] ftpFiles, String baseName, String extension) {
        for (FTPFile ftpFile : ftpFiles) {
            if (ftpFile.isDirectory()) {
                continue;
            }
            String fileName = ftpFile.getName();
            String lowerFileName = fileName.toLowerCase();
            String targetFileName = (baseName + extension).toLowerCase();
            
            if (lowerFileName.equals(targetFileName)) {
                return fileName;
            }
        }
        return null;
    }
    
    /**
     * 在SCP文件列表中查找匹配的文件名（忽略大小写）
     * 
     * @param scpFiles SCP文件列表
     * @param baseName 基础文件名（不包含扩展名）
     * @param extension 扩展名（如".cfg"或".dat"）
     * @return 匹配的文件名，如果未找到则返回null
     */
    private String findMatchingScpFile(List<String> scpFiles, String baseName, String extension) {
        String targetFileName = (baseName + extension).toLowerCase();
        
        for (String fileName : scpFiles) {
            String lowerFileName = fileName.toLowerCase();
            if (lowerFileName.equals(targetFileName)) {
                return fileName;
            }
        }
        return null;
    }

    /**
     * 获取指定厂商的传输配置，如果不存在则返回默认配置
     * 
     * @param vendor 厂商名称
     * @return 厂商传输配置对象
     */
    private VendorTransferCfg getVendorConfigOrDefault(String vendor) {
        Map<String, VendorTransferCfg> configMap = getVendorTransferConfig();
        VendorTransferCfg vendorConfig = configMap.get(vendor);
        if (vendorConfig == null) {
            vendorConfig = configMap.get("DEFAULT");
        }
        
        // 创建配置对象并设置默认值
        VendorTransferCfg config = new VendorTransferCfg();
        String protocol = "FTP";
        Integer port = 21;
        String username = "";
        String password = "";
        String directory = "/comtrade";
        
        if (vendorConfig != null) {
            if (StrUtil.isNotBlank(vendorConfig.getProtocol())) {
                protocol = vendorConfig.getProtocol().trim();
            }
            if (vendorConfig.getPort() != null && vendorConfig.getPort() > 0) {
                port = vendorConfig.getPort();
            } else {
                // 根据协议设置默认端口
                port = "SCP".equalsIgnoreCase(protocol) ? 22 : 21;
            }
            if (StrUtil.isNotBlank(vendorConfig.getUsername())) {
                username = vendorConfig.getUsername().trim();
            }
            if (StrUtil.isNotBlank(vendorConfig.getPassword())) {
                password = vendorConfig.getPassword().trim();
            }
            if (StrUtil.isNotBlank(vendorConfig.getDirectory())) {
                directory = vendorConfig.getDirectory().trim();
                // 确保目录以/开头
                if (!directory.startsWith("/")) {
                    directory = "/" + directory;
                }
            }
        }
        
        config.setProtocol(protocol);
        config.setPort(port);
        config.setUsername(username);
        config.setPassword(password);
        config.setDirectory(directory);
        
        return config;
    }
    
    /**
     * 从系统配置中获取厂商传输配置
     * 
     * @return 厂商传输配置Map，key为厂商名称，value为传输配置对象
     */
    private Map<String, VendorTransferCfg> getVendorTransferConfig() {
        Map<String, VendorTransferCfg> configMap = new HashMap<>();
        try {
            SystemConfig config = systemConfigService.findBySystemConfigKey("comtrade.vendor.transfer.config");
            if (config != null && StrUtil.isNotBlank(config.getSystemConfigValue())) {
                String configValue = config.getSystemConfigValue().trim();
                log.info("从系统配置获取厂商传输配置: {}", configValue);
                
                // 解析JSON字符串为Map
                Map<String, Object> jsonMap = JSONUtil.toBean(configValue, Map.class);
                
                // 将每个厂商配置转换为VendorTransferCfg对象
                for (Map.Entry<String, Object> entry : jsonMap.entrySet()) {
                    String vendorName = entry.getKey();
                    Object vendorConfigObj = entry.getValue();
                    
                    if (vendorConfigObj instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> vendorConfigMap = (Map<String, Object>) vendorConfigObj;
                        
                        VendorTransferCfg vendorConfig = new VendorTransferCfg();
                        vendorConfig.setProtocol((String) vendorConfigMap.get("protocol"));
                        vendorConfig.setPort((Integer) vendorConfigMap.get("port"));
                        vendorConfig.setUsername((String) vendorConfigMap.get("username"));
                        vendorConfig.setPassword((String) vendorConfigMap.get("password"));
                        vendorConfig.setDirectory((String) vendorConfigMap.get("directory"));
                        
                        configMap.put(vendorName, vendorConfig);
                    }
                }
                
                log.info("成功解析厂商传输配置，共{}个厂商", configMap.size());
            } else {
                log.warn("未找到厂商传输配置或配置值为空");
            }
        } catch (Exception e) {
            log.error("解析厂商传输配置失败", e);
        }
        
        return configMap;
    }

}
